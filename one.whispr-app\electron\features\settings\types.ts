import { BaseEntity } from '../../database/repositories/base';

export interface AudioDevice {
  id: string;
  name: string;
  type: 'input' | 'output';
  isDefault: boolean;
}

export interface Settings extends BaseEntity {
  id: string;
  // Audio devices
  selectedInputDevice: string;
  selectedOutputDevice: string;
  useDefaultInputDevice: boolean;
  useDefaultOutputDevice: boolean;
  enableSoundEffects: boolean;
  volume: number;
  availableInputDevices: AudioDevice[];
  availableOutputDevices: AudioDevice[];
  
  // UI/App preferences
  theme: string;
  accentColor: string;
  autoStart: boolean;
  openSettingsOnStart: boolean;
  notifications: boolean;
  privacyMode: boolean;
  
  // Recording behavior
  recordingMode: 'toggle' | 'pushToTalk';
  shortcuts: {
    pushToTalk: string[];
    toggle: string[];
    cancel: string[];
    modeSwitch: string[];
  };
  
  // Global AI settings
  globalSystemPrompt: string;


}

// Default settings values
export const DEFAULT_SETTINGS: Settings = {
  id: 'default-settings',
  
  // Audio devices
  selectedInputDevice: 'default',
  selectedOutputDevice: 'default',
  useDefaultInputDevice: true,
  useDefaultOutputDevice: true,
  enableSoundEffects: true,
  volume: 100,
  availableInputDevices: [],
  availableOutputDevices: [],
  
  // UI/App preferences
  theme: 'system',
  accentColor: '#3b82f6',
  autoStart: false,
  openSettingsOnStart: false,
  notifications: true,
  privacyMode: false,
  
  // Recording behavior
  recordingMode: 'pushToTalk',
  shortcuts: {
    pushToTalk: ['Control', 'Alt'],
    toggle: ['Control', 'Alt'],
    cancel: ['Escape'],
    modeSwitch: ['Control', 'Space']
  },
  
  // Global AI settings
  globalSystemPrompt: 'Please clean up this transcription by removing filler words and improving grammar while preserving the original meaning.'
}; 