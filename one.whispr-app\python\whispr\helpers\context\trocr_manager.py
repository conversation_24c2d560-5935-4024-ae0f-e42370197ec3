"""
TROCR Manager

Manages TROCR (Transformer-based Optical Character Recognition) model
for text extraction from preprocessed images.
"""

import logging
import time
import asyncio
from typing import Dict, Any, Optional, List
from pathlib import Path
import numpy as np

logger = logging.getLogger(__name__)

try:
    import torch
    from transformers import TrOCRProcessor, VisionEncoderDecoderModel
    from PIL import Image
    TROCR_AVAILABLE = True
except ImportError:
    TROCR_AVAILABLE = False
    logger.warning("TROCR dependencies not available. Install with: pip install transformers torch pillow")


class TROCRManager:
    """Manages TROCR model for optical character recognition."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the TROCR manager.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.TROCRManager")
        
        # Model components
        self.processor = None
        self.model = None
        self.device = None
        
        # Configuration
        self.model_name = self.config.get('model_name', 'microsoft/trocr-small-stage1')
        self.model_path = self.config.get('model_path', 'models/trocr-small-stage1')
        self.confidence_threshold = self.config.get('confidence_threshold', 0.7)
        self.max_length = self.config.get('max_length', 512)
        self.use_gpu = self.config.get('use_gpu', True)
        
        # Performance tracking
        self.inference_count = 0
        self.total_inference_time = 0
        self.last_inference_time = 0
        
        # State
        self.is_loaded = False
        
        self.logger.debug("TROCRManager initialized")
    
    async def initialize(self) -> bool:
        """Initialize the TROCR model asynchronously."""
        try:
            if not TROCR_AVAILABLE:
                self.logger.error("TROCR dependencies not available")
                return False
            
            self.logger.info("Loading TROCR model...")
            
            # Determine device
            if self.use_gpu and torch.cuda.is_available():
                self.device = torch.device("cuda")
                self.logger.info("Using GPU for TROCR inference")
            else:
                self.device = torch.device("cpu")
                self.logger.info("Using CPU for TROCR inference")
            
            # Check if local model exists
            local_model_path = Path(self.model_path)
            if local_model_path.exists() and local_model_path.is_dir() and any(local_model_path.iterdir()):
                self.logger.info(f"Loading local TROCR model from {local_model_path}")
                model_source = str(local_model_path)
            else:
                self.logger.info(f"Local model not found at {local_model_path}")
                self.logger.info(f"Downloading TROCR model from Hugging Face: {self.model_name}")
                self.logger.info("This may take several minutes on first run...")
                model_source = self.model_name

                # Create local model directory for caching
                local_model_path.mkdir(parents=True, exist_ok=True)
            
            # Load model and processor in executor to avoid blocking
            loop = asyncio.get_event_loop()

            # Load processor
            self.logger.info("Loading TROCR processor...")
            self.processor = await loop.run_in_executor(
                None,
                lambda: TrOCRProcessor.from_pretrained(
                    model_source,
                    cache_dir=str(local_model_path.parent) if model_source == self.model_name else None
                )
            )

            # Load model
            self.logger.info("Loading TROCR model...")
            self.model = await loop.run_in_executor(
                None,
                lambda: VisionEncoderDecoderModel.from_pretrained(
                    model_source,
                    cache_dir=str(local_model_path.parent) if model_source == self.model_name else None
                )
            )
            
            # Move model to device
            self.model = self.model.to(self.device)
            self.model.eval()  # Set to evaluation mode
            
            self.is_loaded = True
            self.logger.info("TROCR model loaded successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Error initializing TROCR model: {e}")
            return False
    
    async def extract_text(self, image: np.ndarray) -> Optional[Dict[str, Any]]:
        """Extract text from an image using TROCR.
        
        Args:
            image: Input image as numpy array (RGB format)
            
        Returns:
            Dictionary with extracted text and confidence, or None if failed
        """
        try:
            if not self.is_loaded:
                self.logger.error("TROCR model not loaded")
                return None
            
            if image is None:
                return None
            
            start_time = time.time()
            
            # Convert numpy array to PIL Image
            if isinstance(image, np.ndarray):
                pil_image = Image.fromarray(image)
            else:
                pil_image = image
            
            # Ensure image is in RGB mode
            if pil_image.mode != 'RGB':
                pil_image = pil_image.convert('RGB')
            
            # Run inference in executor to avoid blocking
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, self._run_inference, pil_image)
            
            # Update performance metrics
            inference_time = time.time() - start_time
            self.last_inference_time = inference_time
            self.inference_count += 1
            self.total_inference_time += inference_time
            
            if result:
                self.logger.debug(f"Text extracted: '{result['text'][:50]}...', confidence: {result['confidence']:.3f}, time: {inference_time:.3f}s")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error extracting text: {e}")
            return None
    
    def _run_inference(self, image: Image.Image) -> Optional[Dict[str, Any]]:
        """Run TROCR inference on a PIL image (synchronous)."""
        try:
            # Preprocess image
            pixel_values = self.processor(image, return_tensors="pt").pixel_values
            pixel_values = pixel_values.to(self.device)
            
            # Generate text
            with torch.no_grad():
                generated_ids = self.model.generate(
                    pixel_values,
                    max_length=self.max_length,
                    num_beams=4,
                    early_stopping=True,
                    return_dict_in_generate=True,
                    output_scores=True
                )
            
            # Decode generated text
            generated_text = self.processor.batch_decode(
                generated_ids.sequences, 
                skip_special_tokens=True
            )[0]
            
            # Calculate confidence (approximate from scores)
            confidence = self._calculate_confidence(generated_ids.scores)
            
            # Filter by confidence threshold
            if confidence < self.confidence_threshold:
                self.logger.debug(f"Text confidence {confidence:.3f} below threshold {self.confidence_threshold}")
                return {
                    "text": "",
                    "confidence": confidence,
                    "raw_text": generated_text
                }
            
            return {
                "text": generated_text.strip(),
                "confidence": confidence,
                "raw_text": generated_text
            }
            
        except Exception as e:
            self.logger.error(f"Error in TROCR inference: {e}")
            return None
    
    def _calculate_confidence(self, scores: List[torch.Tensor]) -> float:
        """Calculate confidence score from model output scores.
        
        Args:
            scores: List of score tensors from model generation
            
        Returns:
            Confidence score between 0 and 1
        """
        try:
            if not scores:
                return 0.0
            
            # Calculate average probability across all tokens
            total_prob = 0.0
            total_tokens = 0
            
            for score in scores:
                # Apply softmax to get probabilities
                probs = torch.softmax(score, dim=-1)
                # Get max probability for each position
                max_probs = torch.max(probs, dim=-1)[0]
                # Average across batch
                avg_prob = torch.mean(max_probs).item()
                
                total_prob += avg_prob
                total_tokens += 1
            
            if total_tokens == 0:
                return 0.0
            
            confidence = total_prob / total_tokens
            return min(max(confidence, 0.0), 1.0)  # Clamp between 0 and 1
            
        except Exception as e:
            self.logger.error(f"Error calculating confidence: {e}")
            return 0.0
    
    def extract_text_batch(self, images: List[np.ndarray]) -> List[Optional[Dict[str, Any]]]:
        """Extract text from multiple images (synchronous batch processing).
        
        Args:
            images: List of images as numpy arrays
            
        Returns:
            List of extraction results
        """
        try:
            if not self.is_loaded:
                self.logger.error("TROCR model not loaded")
                return [None] * len(images)
            
            results = []
            for image in images:
                if isinstance(image, np.ndarray):
                    pil_image = Image.fromarray(image)
                else:
                    pil_image = image
                
                if pil_image.mode != 'RGB':
                    pil_image = pil_image.convert('RGB')
                
                result = self._run_inference(pil_image)
                results.append(result)
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error in batch text extraction: {e}")
            return [None] * len(images)
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model.
        
        Returns:
            Dictionary with model information
        """
        return {
            "model_name": self.model_name,
            "model_path": self.model_path,
            "device": str(self.device) if self.device else None,
            "is_loaded": self.is_loaded,
            "confidence_threshold": self.confidence_threshold,
            "max_length": self.max_length
        }
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics.
        
        Returns:
            Dictionary with performance metrics
        """
        avg_time = self.total_inference_time / self.inference_count if self.inference_count > 0 else 0
        
        return {
            "inference_count": self.inference_count,
            "total_time": self.total_inference_time,
            "average_time": avg_time,
            "last_inference_time": self.last_inference_time,
            "throughput": 1.0 / avg_time if avg_time > 0 else 0
        }
    
    def update_config(self, config: Dict[str, Any]) -> None:
        """Update configuration.
        
        Args:
            config: New configuration dictionary
        """
        self.config.update(config)
        
        # Update settings
        self.confidence_threshold = config.get('confidence_threshold', self.confidence_threshold)
        self.max_length = config.get('max_length', self.max_length)
        
        self.logger.debug("Configuration updated")
    
    async def cleanup(self) -> None:
        """Clean up resources."""
        try:
            if self.model:
                # Move model to CPU and clear cache
                self.model = self.model.cpu()
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                del self.model
                self.model = None
            
            if self.processor:
                del self.processor
                self.processor = None
            
            self.device = None
            self.is_loaded = False
            
            self.logger.debug("TROCR manager cleanup completed")
            
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
