#!/usr/bin/env node
/**
 * Copy models from python/models to .dist/models for development builds
 * This ensures the same model structure is used in both development and production
 * Currently copies pyannote models for speaker diarization
 */

import * as fs from 'fs';
import * as path from 'path';
import ora from 'ora';

const projectRoot = process.cwd();
const sourceModelsDir = path.join(projectRoot, 'python', 'models');
const targetModelsDir = path.join(projectRoot, '.dist', 'models');

/**
 * Recursively copy directory contents
 */
async function copyDirectory(source: string, target: string): Promise<void> {
  // Create target directory if it doesn't exist
  if (!fs.existsSync(target)) {
    fs.mkdirSync(target, { recursive: true });
  }

  const items = fs.readdirSync(source);

  for (const item of items) {
    const sourcePath = path.join(source, item);
    const targetPath = path.join(target, item);
    const stat = fs.statSync(sourcePath);

    if (stat.isDirectory()) {
      // Recursively copy subdirectory
      await copyDirectory(sourcePath, targetPath);
    } else {
      // Copy file
      fs.copyFileSync(sourcePath, targetPath);
    }
  }
}

/**
 * Get directory size in bytes
 */
function getDirectorySize(dirPath: string): number {
  let totalSize = 0;

  if (!fs.existsSync(dirPath)) {
    return 0;
  }

  const items = fs.readdirSync(dirPath);

  for (const item of items) {
    const itemPath = path.join(dirPath, item);
    const stat = fs.statSync(itemPath);

    if (stat.isDirectory()) {
      totalSize += getDirectorySize(itemPath);
    } else {
      totalSize += stat.size;
    }
  }

  return totalSize;
}

/**
 * Format bytes to human readable string
 */
function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Check if models need to be copied (source is newer or target doesn't exist)
 */
function needsCopy(): boolean {
  if (!fs.existsSync(targetModelsDir)) {
    return true;
  }

  if (!fs.existsSync(sourceModelsDir)) {
    return false;
  }

  // Check if source directory is newer than target
  const sourceStats = fs.statSync(sourceModelsDir);
  const targetStats = fs.statSync(targetModelsDir);

  return sourceStats.mtime > targetStats.mtime;
}

/**
 * Main function to copy models
 */
async function copyModels(): Promise<void> {
  const spinner = ora('Checking models...').start();

  try {
    // Check if source models directory exists
    if (!fs.existsSync(sourceModelsDir)) {
      spinner.info('📁 No models found in python/models - skipping copy');
      return;
    }

    // Check if copy is needed
    if (!needsCopy()) {
      spinner.succeed('✅ Models are up to date in .dist/models');
      return;
    }

    // Get source directory size
    const sourceSize = getDirectorySize(sourceModelsDir);
    
    if (sourceSize === 0) {
      spinner.info('📁 Models directory is empty - skipping copy');
      return;
    }

    spinner.text = `📦 Copying models (${formatBytes(sourceSize)})...`;

    // Remove existing target directory if it exists
    if (fs.existsSync(targetModelsDir)) {
      fs.rmSync(targetModelsDir, { recursive: true, force: true });
    }

    // Copy models
    await copyDirectory(sourceModelsDir, targetModelsDir);

    // Verify copy
    const targetSize = getDirectorySize(targetModelsDir);
    
    if (targetSize !== sourceSize) {
      throw new Error(`Copy verification failed: source ${formatBytes(sourceSize)} != target ${formatBytes(targetSize)}`);
    }

    spinner.succeed(`✅ Models copied successfully (${formatBytes(targetSize)})`);

    // List copied models
    const models = fs.readdirSync(targetModelsDir);
    if (models.length > 0) {
      console.log('📋 Copied models:');
      for (const model of models) {
        const modelPath = path.join(targetModelsDir, model);
        const stat = fs.statSync(modelPath);
        if (stat.isDirectory()) {
          const modelSize = getDirectorySize(modelPath);
          console.log(`  • ${model} (${formatBytes(modelSize)})`);
        }
      }
    }

  } catch (error) {
    spinner.fail(`❌ Failed to copy models: ${error instanceof Error ? error.message : String(error)}`);
    process.exit(1);
  }
}

/**
 * Clean models directory
 */
async function cleanModels(): Promise<void> {
  const spinner = ora('Cleaning models...').start();

  try {
    if (fs.existsSync(targetModelsDir)) {
      fs.rmSync(targetModelsDir, { recursive: true, force: true });
      spinner.succeed('✅ Models directory cleaned');
    } else {
      spinner.info('📁 Models directory already clean');
    }
  } catch (error) {
    spinner.fail(`❌ Failed to clean models: ${error instanceof Error ? error.message : String(error)}`);
    process.exit(1);
  }
}

// Main execution
async function main(): Promise<void> {
  const command = process.argv[2];

  switch (command) {
    case 'clean':
      await cleanModels();
      break;
    case 'copy':
    case undefined:
      await copyModels();
      break;
    default:
      console.log('Usage: tsx copy-models.ts [copy|clean]');
      console.log('  copy (default): Copy models from python/models to .dist/models');
      console.log('  clean: Remove .dist/models directory');
      process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Unexpected error:', error);
    process.exit(1);
  });
}

export { copyModels, cleanModels };
