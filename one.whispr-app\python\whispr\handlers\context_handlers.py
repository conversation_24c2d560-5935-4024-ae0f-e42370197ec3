"""
Context command handlers for One.Whispr.

This module contains handlers for context awareness (OCR) commands.
"""

import logging
from typing import Dict, Any, TYPE_CHECKING

from whispr.core.response_utils import success_response, error_response, ErrorCodes

if TYPE_CHECKING:
    from whispr.core.handlers import CommandHandlers

# Configure logging
logger = logging.getLogger('whispr.handlers.context')


def _get_context_service(context: Dict[str, Any]):
    """Get the context service from the context.
    
    Args:
        context: Execution context
        
    Returns:
        ContextService instance or None if not available
    """
    service_container = context.get('service_container')
    if not service_container:
        return None
    return service_container.resolve("context")


async def handle_context_start_capture(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle starting continuous context capture.
    
    Args:
        params: The message parameters
        context: The execution context
        
    Returns:
        The response data
    """
    try:
        context_service = _get_context_service(context)
        if not context_service:
            return error_response(ErrorCodes.SERVICE_UNAVAILABLE, "Context service not available")
        
        result = await context_service.start_capture()
        
        if result.get("success"):
            return success_response(result, "Context capture started")
        else:
            return error_response(ErrorCodes.OPERATION_FAILED, result.get("error", "Failed to start capture"))
            
    except Exception as e:
        logger.error(f"Error starting context capture: {e}")
        return error_response(ErrorCodes.INTERNAL_ERROR, str(e))


async def handle_context_stop_capture(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle stopping continuous context capture.
    
    Args:
        params: The message parameters
        context: The execution context
        
    Returns:
        The response data
    """
    try:
        context_service = _get_context_service(context)
        if not context_service:
            return error_response(ErrorCodes.SERVICE_UNAVAILABLE, "Context service not available")
        
        result = await context_service.stop_capture()
        
        if result.get("success"):
            return success_response(result, "Context capture stopped")
        else:
            return error_response(ErrorCodes.OPERATION_FAILED, result.get("error", "Failed to stop capture"))
            
    except Exception as e:
        logger.error(f"Error stopping context capture: {e}")
        return error_response(ErrorCodes.INTERNAL_ERROR, str(e))


async def handle_context_capture_once(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle single context capture.
    
    Args:
        params: The message parameters
        context: The execution context
        
    Returns:
        The response data
    """
    try:
        context_service = _get_context_service(context)
        if not context_service:
            return error_response(ErrorCodes.SERVICE_UNAVAILABLE, "Context service not available")
        
        result = await context_service.capture_once()
        
        if result.get("success"):
            return success_response(result.get("data", {}), "Context captured successfully")
        else:
            return error_response(ErrorCodes.OPERATION_FAILED, result.get("error", "Failed to capture context"))
            
    except Exception as e:
        logger.error(f"Error capturing context: {e}")
        return error_response(ErrorCodes.INTERNAL_ERROR, str(e))


async def handle_context_get_current_text(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle getting current extracted text.
    
    Args:
        params: The message parameters
        context: The execution context
        
    Returns:
        The response data
    """
    try:
        context_service = _get_context_service(context)
        if not context_service:
            return error_response(ErrorCodes.SERVICE_UNAVAILABLE, "Context service not available")
        
        current_text = context_service.get_current_text()
        
        return success_response({
            "text": current_text,
            "length": len(current_text)
        }, "Current text retrieved")
        
    except Exception as e:
        logger.error(f"Error getting current text: {e}")
        return error_response(ErrorCodes.INTERNAL_ERROR, str(e))


async def handle_context_get_text_history(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle getting text extraction history.
    
    Args:
        params: The message parameters (can include 'limit' for number of entries)
        context: The execution context
        
    Returns:
        The response data
    """
    try:
        context_service = _get_context_service(context)
        if not context_service:
            return error_response(ErrorCodes.SERVICE_UNAVAILABLE, "Context service not available")
        
        limit = params.get('limit', 10)
        if not isinstance(limit, int) or limit < 0:
            limit = 10
        
        history = context_service.get_text_history(limit)
        
        return success_response({
            "history": history,
            "count": len(history)
        }, "Text history retrieved")
        
    except Exception as e:
        logger.error(f"Error getting text history: {e}")
        return error_response(ErrorCodes.INTERNAL_ERROR, str(e))


async def handle_context_get_status(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle getting context service status.
    
    Args:
        params: The message parameters
        context: The execution context
        
    Returns:
        The response data
    """
    try:
        context_service = _get_context_service(context)
        if not context_service:
            return error_response(ErrorCodes.SERVICE_UNAVAILABLE, "Context service not available")
        
        status = context_service.get_status()
        
        return success_response(status, "Context status retrieved")
        
    except Exception as e:
        logger.error(f"Error getting context status: {e}")
        return error_response(ErrorCodes.INTERNAL_ERROR, str(e))


async def handle_context_update_config(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle updating context service configuration.
    
    Args:
        params: The message parameters (should include 'config' dict)
        context: The execution context
        
    Returns:
        The response data
    """
    try:
        context_service = _get_context_service(context)
        if not context_service:
            return error_response(ErrorCodes.SERVICE_UNAVAILABLE, "Context service not available")
        
        config = params.get('config', {})
        if not isinstance(config, dict):
            return error_response(ErrorCodes.INVALID_PARAMS, "Config must be a dictionary")
        
        success = context_service.update_config(config)
        
        if success:
            return success_response({"updated": True}, "Configuration updated")
        else:
            return error_response(ErrorCodes.OPERATION_FAILED, "Failed to update configuration")
            
    except Exception as e:
        logger.error(f"Error updating context config: {e}")
        return error_response(ErrorCodes.INTERNAL_ERROR, str(e))


async def handle_context_get_model_info(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle getting TROCR model information.
    
    Args:
        params: The message parameters
        context: The execution context
        
    Returns:
        The response data
    """
    try:
        context_service = _get_context_service(context)
        if not context_service:
            return error_response(ErrorCodes.SERVICE_UNAVAILABLE, "Context service not available")
        
        # Get model info from TROCR manager if available
        model_info = {}
        if hasattr(context_service, 'trocr_manager') and context_service.trocr_manager:
            model_info = context_service.trocr_manager.get_model_info()
        
        return success_response(model_info, "Model information retrieved")
        
    except Exception as e:
        logger.error(f"Error getting model info: {e}")
        return error_response(ErrorCodes.INTERNAL_ERROR, str(e))


async def handle_context_get_performance_stats(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle getting context service performance statistics.
    
    Args:
        params: The message parameters
        context: The execution context
        
    Returns:
        The response data
    """
    try:
        context_service = _get_context_service(context)
        if not context_service:
            return error_response(ErrorCodes.SERVICE_UNAVAILABLE, "Context service not available")
        
        stats = {}
        
        # Get stats from all components if available
        if hasattr(context_service, 'screenshot_manager') and context_service.screenshot_manager:
            stats['screenshot'] = context_service.screenshot_manager.get_performance_stats()
        
        if hasattr(context_service, 'image_processor') and context_service.image_processor:
            stats['image_processing'] = context_service.image_processor.get_performance_stats()
        
        if hasattr(context_service, 'trocr_manager') and context_service.trocr_manager:
            stats['trocr'] = context_service.trocr_manager.get_performance_stats()
        
        return success_response(stats, "Performance statistics retrieved")
        
    except Exception as e:
        logger.error(f"Error getting performance stats: {e}")
        return error_response(ErrorCodes.INTERNAL_ERROR, str(e))


def register_handlers(command_handlers: 'CommandHandlers') -> None:
    """Register context handlers with the command handlers.
    
    Args:
        command_handlers: The CommandHandlers instance to register with
    """
    logger.debug("Registering context handlers")
    
    # Core context operations
    command_handlers.registry.register_function("context.start_capture", handle_context_start_capture)
    command_handlers.registry.register_function("context.stop_capture", handle_context_stop_capture)
    command_handlers.registry.register_function("context.capture_once", handle_context_capture_once)
    
    # Text retrieval
    command_handlers.registry.register_function("context.get_current_text", handle_context_get_current_text)
    command_handlers.registry.register_function("context.get_text_history", handle_context_get_text_history)
    
    # Status and configuration
    command_handlers.registry.register_function("context.get_status", handle_context_get_status)
    command_handlers.registry.register_function("context.update_config", handle_context_update_config)
    
    # Model and performance info
    command_handlers.registry.register_function("context.get_model_info", handle_context_get_model_info)
    command_handlers.registry.register_function("context.get_performance_stats", handle_context_get_performance_stats)
    
    logger.info("Context handlers registered successfully")
