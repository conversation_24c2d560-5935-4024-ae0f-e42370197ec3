{"name": "OneWhispr", "version": "1.0.0", "description": "One Whispr - AI-powered transcription and productivity app", "author": "One Whispr Team", "private": true, "main": ".dist/main/electron/main.js", "scripts": {"dev": "cross-env NODE_ENV=development concurrently --kill-others --names \"VITE,ELECTRON\" --prefix-colors \"blue,green\" \"vite\" \"npm run _dev:electron\"", "dev:no-reload": "cross-env NODE_ENV=development npm run build && cross-env LOAD_FROM_VITE=false electron .", "build": "npm run build:renderer && npm run build:main && npm run build:models", "build:renderer": "vite build", "build:main": "tsc -p tsconfig.electron.json && npm run _build:preload", "build:models": "tsx scripts/copy-models.ts", "build:dist": "npm run build && electron-builder --config electron-builder.json", "_dev:electron": "npm run build:main && electron .", "_build:preload": "tsx scripts/build-preload.ts", "backend:setup": "tsx python/utils/backend-setup.ts", "backend:compile": "tsx python/utils/backend-compile.ts", "backend:compile:quick": "tsx python/utils/backend-compile.ts --quick-update", "postinstall": "electron-builder install-app-deps && npx @electron/rebuild"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.6", "better-sqlite3": "^11.9.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "electron-store": "^8.2.0", "lodash": "^4.17.21", "motion": "^12.6.3", "nanoid": "^3.3.7", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.55.0", "react-icons": "^5.5.0", "react-router-dom": "^7.5.0", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5", "ws": "^8.18.2", "zod": "^3.24.2"}, "devDependencies": {"@electron/rebuild": "^4.0.1", "@octokit/rest": "^21.1.1", "@stagewise/toolbar-react": "^0.4.5", "@tailwindcss/vite": "^4.1.3", "@types/better-sqlite3": "^7.6.13", "@types/lodash": "^4.17.16", "@types/node": "^22.14.0", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.2", "@types/ws": "^8.5.10", "@vitejs/plugin-react": "^4.3.4", "7zip-bin": "^5.2.0", "concurrently": "^8.2.1", "cross-env": "^7.0.3", "electron": "^35.0.0", "electron-builder": "^26.0.12", "tailwindcss": "^4.1.3", "tar": "^7.4.3", "tsx": "^4.19.3", "typescript": "^5.8.3", "vite": "^6.2.6"}}