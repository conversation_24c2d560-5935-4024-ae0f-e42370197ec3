"""
Image Processor

Preprocesses screenshots for optimal OCR performance using OpenCV.
Includes resizing, denoising, contrast adjustment, and text enhancement.
"""

import logging
import time
from typing import Dict, Any, Optional, Tuple
import numpy as np

logger = logging.getLogger(__name__)

try:
    import cv2
    from PIL import Image
    OPENCV_AVAILABLE = True
except ImportError:
    OPENCV_AVAILABLE = False
    logger.warning("OpenCV or PIL not available. Install with: pip install opencv-python pillow")


class ImageProcessor:
    """Preprocesses images for optimal OCR performance."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the image processor.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.ImageProcessor")
        
        # Configuration
        self.target_width = self.config.get('target_width', 1920)
        self.target_height = self.config.get('target_height', 1080)
        self.quality_mode = self.config.get('image_quality', 'medium')  # low, medium, high
        self.enable_denoising = self.config.get('enable_denoising', True)
        self.enable_contrast = self.config.get('enable_contrast', True)
        self.enable_sharpening = self.config.get('enable_sharpening', True)
        
        # Performance tracking
        self.processing_count = 0
        self.total_processing_time = 0
        self.last_processing_time = 0
        
        self.logger.debug("ImageProcessor initialized")
    
    def initialize(self) -> bool:
        """Initialize the image processor."""
        try:
            if not OPENCV_AVAILABLE:
                self.logger.error("OpenCV library not available")
                return False
            
            self.logger.info(f"Image processor initialized with quality mode: {self.quality_mode}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error initializing image processor: {e}")
            return False
    
    def preprocess(self, image: np.ndarray) -> Optional[np.ndarray]:
        """Preprocess an image for OCR.
        
        Args:
            image: Input image as numpy array (RGB format)
            
        Returns:
            Preprocessed image as numpy array or None if failed
        """
        try:
            if image is None:
                return None
            
            start_time = time.time()
            
            # Convert RGB to BGR for OpenCV
            img_bgr = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
            
            # Apply preprocessing based on quality mode
            if self.quality_mode == 'low':
                processed = self._preprocess_low_quality(img_bgr)
            elif self.quality_mode == 'high':
                processed = self._preprocess_high_quality(img_bgr)
            else:  # medium
                processed = self._preprocess_medium_quality(img_bgr)
            
            # Convert back to RGB
            if processed is not None:
                processed_rgb = cv2.cvtColor(processed, cv2.COLOR_BGR2RGB)
            else:
                processed_rgb = None
            
            # Update performance metrics
            processing_time = time.time() - start_time
            self.last_processing_time = processing_time
            self.processing_count += 1
            self.total_processing_time += processing_time
            
            if processed_rgb is not None:
                self.logger.debug(f"Image preprocessed: {processed_rgb.shape}, time: {processing_time:.3f}s")
            else:
                self.logger.error("Image preprocessing failed")
            
            return processed_rgb
            
        except Exception as e:
            self.logger.error(f"Error preprocessing image: {e}")
            return None
    
    def _preprocess_low_quality(self, image: np.ndarray) -> Optional[np.ndarray]:
        """Fast preprocessing for low quality mode."""
        try:
            # Simple resize if needed
            height, width = image.shape[:2]
            if width > self.target_width or height > self.target_height:
                scale = min(self.target_width / width, self.target_height / height)
                new_width = int(width * scale)
                new_height = int(height * scale)
                image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_LINEAR)
            
            # Basic contrast enhancement
            if self.enable_contrast:
                image = cv2.convertScaleAbs(image, alpha=1.2, beta=10)
            
            return image
            
        except Exception as e:
            self.logger.error(f"Error in low quality preprocessing: {e}")
            return None
    
    def _preprocess_medium_quality(self, image: np.ndarray) -> Optional[np.ndarray]:
        """Balanced preprocessing for medium quality mode."""
        try:
            # Resize if needed
            height, width = image.shape[:2]
            if width > self.target_width or height > self.target_height:
                scale = min(self.target_width / width, self.target_height / height)
                new_width = int(width * scale)
                new_height = int(height * scale)
                image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
            
            # Denoising
            if self.enable_denoising:
                image = cv2.fastNlMeansDenoisingColored(image, None, 10, 10, 7, 21)
            
            # Contrast enhancement using CLAHE
            if self.enable_contrast:
                lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
                l, a, b = cv2.split(lab)
                clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
                l = clahe.apply(l)
                image = cv2.merge([l, a, b])
                image = cv2.cvtColor(image, cv2.COLOR_LAB2BGR)
            
            # Mild sharpening
            if self.enable_sharpening:
                kernel = np.array([[-1, -1, -1],
                                 [-1,  9, -1],
                                 [-1, -1, -1]])
                image = cv2.filter2D(image, -1, kernel)
            
            return image
            
        except Exception as e:
            self.logger.error(f"Error in medium quality preprocessing: {e}")
            return None
    
    def _preprocess_high_quality(self, image: np.ndarray) -> Optional[np.ndarray]:
        """Comprehensive preprocessing for high quality mode."""
        try:
            # High-quality resize if needed
            height, width = image.shape[:2]
            if width > self.target_width or height > self.target_height:
                scale = min(self.target_width / width, self.target_height / height)
                new_width = int(width * scale)
                new_height = int(height * scale)
                image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
            
            # Advanced denoising
            if self.enable_denoising:
                # Apply bilateral filter first
                image = cv2.bilateralFilter(image, 9, 75, 75)
                # Then apply Non-local Means Denoising
                image = cv2.fastNlMeansDenoisingColored(image, None, 10, 10, 7, 21)
            
            # Advanced contrast enhancement
            if self.enable_contrast:
                # Convert to LAB color space
                lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
                l, a, b = cv2.split(lab)
                
                # Apply CLAHE to L channel
                clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
                l = clahe.apply(l)
                
                # Merge and convert back
                image = cv2.merge([l, a, b])
                image = cv2.cvtColor(image, cv2.COLOR_LAB2BGR)
                
                # Additional gamma correction
                gamma = 1.2
                inv_gamma = 1.0 / gamma
                table = np.array([((i / 255.0) ** inv_gamma) * 255 for i in np.arange(0, 256)]).astype("uint8")
                image = cv2.LUT(image, table)
            
            # Advanced sharpening
            if self.enable_sharpening:
                # Unsharp masking
                gaussian = cv2.GaussianBlur(image, (0, 0), 2.0)
                image = cv2.addWeighted(image, 1.5, gaussian, -0.5, 0)
            
            # Edge enhancement for text
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            edges = cv2.Canny(gray, 50, 150)
            edges_colored = cv2.cvtColor(edges, cv2.COLOR_GRAY2BGR)
            image = cv2.addWeighted(image, 0.9, edges_colored, 0.1, 0)
            
            return image
            
        except Exception as e:
            self.logger.error(f"Error in high quality preprocessing: {e}")
            return None
    
    def resize_image(self, image: np.ndarray, target_width: int, target_height: int) -> Optional[np.ndarray]:
        """Resize image to target dimensions.
        
        Args:
            image: Input image
            target_width: Target width
            target_height: Target height
            
        Returns:
            Resized image or None if failed
        """
        try:
            return cv2.resize(image, (target_width, target_height), interpolation=cv2.INTER_CUBIC)
        except Exception as e:
            self.logger.error(f"Error resizing image: {e}")
            return None
    
    def enhance_text_regions(self, image: np.ndarray) -> Optional[np.ndarray]:
        """Enhance text regions in the image.
        
        Args:
            image: Input image
            
        Returns:
            Enhanced image or None if failed
        """
        try:
            # Convert to grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Apply morphological operations to enhance text
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
            
            # Closing operation to connect text components
            closed = cv2.morphologyEx(gray, cv2.MORPH_CLOSE, kernel)
            
            # Convert back to BGR
            enhanced = cv2.cvtColor(closed, cv2.COLOR_GRAY2BGR)
            
            return enhanced
            
        except Exception as e:
            self.logger.error(f"Error enhancing text regions: {e}")
            return None
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics.
        
        Returns:
            Dictionary with performance metrics
        """
        avg_time = self.total_processing_time / self.processing_count if self.processing_count > 0 else 0
        
        return {
            "processing_count": self.processing_count,
            "total_time": self.total_processing_time,
            "average_time": avg_time,
            "last_processing_time": self.last_processing_time,
            "quality_mode": self.quality_mode
        }
    
    def update_config(self, config: Dict[str, Any]) -> None:
        """Update configuration.
        
        Args:
            config: New configuration dictionary
        """
        self.config.update(config)
        
        # Update settings
        self.target_width = config.get('target_width', self.target_width)
        self.target_height = config.get('target_height', self.target_height)
        self.quality_mode = config.get('image_quality', self.quality_mode)
        self.enable_denoising = config.get('enable_denoising', self.enable_denoising)
        self.enable_contrast = config.get('enable_contrast', self.enable_contrast)
        self.enable_sharpening = config.get('enable_sharpening', self.enable_sharpening)
        
        self.logger.debug(f"Configuration updated, quality mode: {self.quality_mode}")
    
    def cleanup(self) -> None:
        """Clean up resources."""
        try:
            # OpenCV doesn't require explicit cleanup for basic operations
            self.logger.debug("Image processor cleanup completed")
            
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
