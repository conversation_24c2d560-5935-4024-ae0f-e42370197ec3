"""
Context Service

Provides real-time OCR capabilities using TROCR for context awareness.
Manages screenshot capture, image preprocessing, and text extraction.
"""

import asyncio
import logging
import time
from typing import Dict, List, Any, Optional, Callable
from pathlib import Path

from ..core.base import ConfigurableService, Event
from ..config.manager import ConfigurationMixin
from ..config.settings import DEFAULT_CONFIG

logger = logging.getLogger(__name__)

# Default context service configuration
CONTEXT_SETTINGS = {
    'enabled': False,
    'capture_interval': 5.0,  # seconds between captures
    'max_text_length': 10000,  # maximum characters to store
    'image_quality': 'medium',  # low, medium, high
    'preprocessing_enabled': True,
    'model_name': 'microsoft_trocr-small-stage1',
    'confidence_threshold': 0.7,
    'cache_duration': 300,  # seconds to cache results
}


class ContextService(ConfigurableService, ConfigurationMixin):
    """
    Context awareness service using TROCR for real-time OCR.
    
    Responsibilities:
    - Screenshot capture management
    - Image preprocessing and optimization
    - TROCR model loading and inference
    - Text extraction and caching
    - Configuration and lifecycle management
    """
    
    def __init__(self, service_container=None):
        """Initialize the context service.
        
        Args:
            service_container: The service container for dependency resolution
        """
        super().__init__(service_container)
        
        # Core components (will be initialized in initialize())
        self.screenshot_manager = None
        self.image_processor = None
        self.trocr_manager = None
        
        # State
        self.is_initialized = False
        self.is_capturing = False
        self.capture_task = None
        
        # Data storage
        self.current_text = ""
        self.text_history = []
        self.last_capture_time = 0
        self.cached_results = {}
        
        # Callbacks
        self.text_update_callback = None
        
        self.logger.info("ContextService initialized")
    
    def _get_service_name(self) -> str:
        """Get the service name for configuration purposes."""
        return "context"
    
    def _get_static_defaults(self) -> Dict[str, Any]:
        """Get static default configuration for this service."""
        return CONTEXT_SETTINGS.copy()
    
    async def initialize(self) -> bool:
        """Initialize the context service and all components."""
        try:
            self.logger.info("Initializing ContextService...")
            
            # Load configuration first
            self._load_configuration()
            
            # Check if context awareness is enabled in user settings
            settings = self.get_settings()
            context_enabled = settings.get('contextAwareness', False)
            
            if not context_enabled:
                self.logger.info("Context awareness disabled in user settings")
                self.is_initialized = True
                return True
            
            # Import helpers (lazy import to avoid startup delays)
            try:
                from ..helpers.context.screenshot_manager import ScreenshotManager
                from ..helpers.context.image_processor import ImageProcessor
                from ..helpers.context.trocr_manager import TROCRManager
            except ImportError as e:
                self.logger.error(f"Failed to import context helpers: {e}")
                return False
            
            # Initialize components
            self.screenshot_manager = ScreenshotManager(config=self.get_config())
            self.image_processor = ImageProcessor(config=self.get_config())
            self.trocr_manager = TROCRManager(config=self.get_config())
            
            # Initialize each component
            if not self.screenshot_manager.initialize():
                self.logger.error("Failed to initialize screenshot manager")
                return False
            
            if not self.image_processor.initialize():
                self.logger.error("Failed to initialize image processor")
                return False
            
            if not await self.trocr_manager.initialize():
                self.logger.error("Failed to initialize TROCR manager")
                return False
            
            self.is_initialized = True
            self.logger.info("ContextService initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Error initializing ContextService: {e}")
            return False
    
    async def cleanup(self) -> bool:
        """Clean up resources."""
        try:
            self.logger.info("Cleaning up ContextService...")
            
            # Stop capturing if active
            await self.stop_capture()
            
            # Cleanup components
            if self.trocr_manager:
                await self.trocr_manager.cleanup()
            
            if self.image_processor:
                self.image_processor.cleanup()
            
            if self.screenshot_manager:
                self.screenshot_manager.cleanup()
            
            # Clear data
            self.current_text = ""
            self.text_history.clear()
            self.cached_results.clear()
            
            self.is_initialized = False
            self.logger.info("ContextService cleanup completed")
            return True
            
        except Exception as e:
            self.logger.error(f"Error during ContextService cleanup: {e}")
            return False
    
    async def start_capture(self) -> Dict[str, Any]:
        """Start continuous screenshot capture and OCR processing."""
        try:
            if not self.is_initialized:
                return {"success": False, "error": "Service not initialized"}
            
            if self.is_capturing:
                return {"success": False, "error": "Already capturing"}
            
            # Check if enabled in settings
            settings = self.get_settings()
            if not settings.get('contextAwareness', False):
                return {"success": False, "error": "Context awareness disabled"}
            
            self.is_capturing = True
            self.capture_task = asyncio.create_task(self._capture_loop())
            
            self.logger.info("Started context capture")
            return {"success": True, "message": "Context capture started"}
            
        except Exception as e:
            self.logger.error(f"Error starting capture: {e}")
            return {"success": False, "error": str(e)}
    
    async def stop_capture(self) -> Dict[str, Any]:
        """Stop continuous screenshot capture."""
        try:
            if not self.is_capturing:
                return {"success": False, "error": "Not currently capturing"}
            
            self.is_capturing = False
            
            if self.capture_task:
                self.capture_task.cancel()
                try:
                    await self.capture_task
                except asyncio.CancelledError:
                    pass
                self.capture_task = None
            
            self.logger.info("Stopped context capture")
            return {"success": True, "message": "Context capture stopped"}
            
        except Exception as e:
            self.logger.error(f"Error stopping capture: {e}")
            return {"success": False, "error": str(e)}
    
    async def capture_once(self) -> Dict[str, Any]:
        """Perform a single screenshot capture and OCR."""
        try:
            if not self.is_initialized:
                return {"success": False, "error": "Service not initialized"}
            
            # Check if enabled in settings
            settings = self.get_settings()
            if not settings.get('contextAwareness', False):
                return {"success": False, "error": "Context awareness disabled"}
            
            result = await self._perform_capture()
            return {"success": True, "data": result}
            
        except Exception as e:
            self.logger.error(f"Error in single capture: {e}")
            return {"success": False, "error": str(e)}
    
    def get_current_text(self) -> str:
        """Get the current extracted text."""
        return self.current_text
    
    def get_text_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent text extraction history."""
        return self.text_history[-limit:] if limit > 0 else self.text_history
    
    def set_text_update_callback(self, callback: Callable[[str], None]) -> None:
        """Set callback for text updates."""
        self.text_update_callback = callback
    
    def get_status(self) -> Dict[str, Any]:
        """Get context service status."""
        status = super().get_status()
        
        # Add context-specific status
        settings = self.get_settings()
        status.update({
            "capturing": self.is_capturing,
            "context_enabled": settings.get('contextAwareness', False),
            "current_text_length": len(self.current_text),
            "history_count": len(self.text_history),
            "last_capture": self.last_capture_time,
            "components": {
                "screenshot_manager": self.screenshot_manager is not None,
                "image_processor": self.image_processor is not None,
                "trocr_manager": self.trocr_manager is not None
            }
        })
        
        return status

    # Private methods

    async def _capture_loop(self) -> None:
        """Main capture loop for continuous OCR."""
        try:
            capture_interval = self.get_config('capture_interval', 5.0)

            while self.is_capturing:
                try:
                    await self._perform_capture()
                    await asyncio.sleep(capture_interval)
                except asyncio.CancelledError:
                    break
                except Exception as e:
                    self.logger.error(f"Error in capture loop: {e}")
                    await asyncio.sleep(1.0)  # Brief pause before retry

        except asyncio.CancelledError:
            self.logger.debug("Capture loop cancelled")
        except Exception as e:
            self.logger.error(f"Fatal error in capture loop: {e}")

    async def _perform_capture(self) -> Dict[str, Any]:
        """Perform a single capture, process, and extract text."""
        try:
            start_time = time.time()

            # Capture screenshot
            screenshot = self.screenshot_manager.capture_screen()
            if screenshot is None:
                return {"error": "Failed to capture screenshot"}

            # Preprocess image
            processed_image = self.image_processor.preprocess(screenshot)
            if processed_image is None:
                return {"error": "Failed to preprocess image"}

            # Extract text using TROCR
            extracted_text = await self.trocr_manager.extract_text(processed_image)
            if not extracted_text:
                return {"text": "", "confidence": 0.0}

            # Update current text and history
            self.current_text = extracted_text.get('text', '')
            confidence = extracted_text.get('confidence', 0.0)

            # Add to history
            history_entry = {
                "timestamp": start_time,
                "text": self.current_text,
                "confidence": confidence,
                "processing_time": time.time() - start_time
            }
            self.text_history.append(history_entry)

            # Limit history size
            max_history = self.get_config('max_history_entries', 100)
            if len(self.text_history) > max_history:
                self.text_history = self.text_history[-max_history:]

            # Update last capture time
            self.last_capture_time = start_time

            # Notify callback if set
            if self.text_update_callback:
                try:
                    self.text_update_callback(self.current_text)
                except Exception as e:
                    self.logger.error(f"Error in text update callback: {e}")

            self.logger.debug(f"Captured text: {len(self.current_text)} chars, confidence: {confidence:.2f}")

            return {
                "text": self.current_text,
                "confidence": confidence,
                "timestamp": start_time,
                "processing_time": time.time() - start_time
            }

        except Exception as e:
            self.logger.error(f"Error performing capture: {e}")
            return {"error": str(e)}

    def _clear_old_cache(self) -> None:
        """Clear old cached results."""
        try:
            cache_duration = self.get_config('cache_duration', 300)
            current_time = time.time()

            # Remove old entries
            keys_to_remove = [
                key for key, data in self.cached_results.items()
                if current_time - data.get('timestamp', 0) > cache_duration
            ]

            for key in keys_to_remove:
                del self.cached_results[key]

        except Exception as e:
            self.logger.error(f"Error clearing cache: {e}")

    def update_config(self, config: Dict[str, Any]) -> bool:
        """Update service configuration."""
        try:
            # Refresh configuration
            self._refresh_configuration()

            # Update component configurations if they exist
            if self.screenshot_manager:
                self.screenshot_manager.update_config(self.get_config())

            if self.image_processor:
                self.image_processor.update_config(self.get_config())

            if self.trocr_manager:
                self.trocr_manager.update_config(self.get_config())

            return True

        except Exception as e:
            self.logger.error(f"Error updating configuration: {e}")
            return False
